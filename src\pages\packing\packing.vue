<!-- 装箱作业页面 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '装箱作业',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="packing-page bg-gray-50 min-h-screen">
    <!-- 工单选择区域 -->
    <view class="work-order-section bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">选择包装工单</view>
      <wd-select-picker
        v-model="selectedWorkOrder"
        :columns="workOrderOptions"
        filterable
        type="radio"
        title="选择工单"
        placeholder="请选择工单"
        :loading="loading"
        @open="loadWorkOrderList"
        @confirm="onWorkOrderChange"
      />
    </view>

    <!-- 工单信息展示区域 -->
    <view v-if="workOrderInfo" class="work-order-info bg-white p-4 mb-2">
      <wd-row :gutter="12" class="mb-3">
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">产品名称</view>
            <wd-input
              v-model="workOrderInfo.productName"
              placeholder="产品名称"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">供货批次码</view>
            <wd-input
              v-model="workOrderInfo.lotNo"
              placeholder="供货批次号"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
      </wd-row>

      <wd-row :gutter="12" class="mb-3">
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">零件号</view>
            <wd-input
              v-model="workOrderInfo.partCode"
              placeholder="零件号"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">工单编号</view>
            <wd-input
              v-model="workOrderInfo.workNo"
              placeholder="工单编号"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">工单类型</view>
            <wd-input
              v-model="workOrderInfo.packageCategory"
              placeholder="工单类型"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
        <wd-col :span="12">
          <view class="info-item">
            <view class="label text-sm text-gray-600 mb-1">包装总数</view>
            <wd-input
              v-model="workOrderInfo.amount"
              placeholder="包装总数"
              readonly
              class="info-input"
            />
          </view>
        </wd-col>
      </wd-row>
    </view>

    <!-- 产品码录入区域 -->
    <view class="product-input-section bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">
        产品码录入
        <span style="font-size: large">({{ `数量:${workOrderInfo.singleQuantity}` }})</span>
      </view>
      <view class="input-container flex items-center gap-2">
        <wd-input
          v-model="productCode"
          placeholder="请扫描或输入产品码"
          class="flex-1"
          clearable
          @confirm="addProductCode"
        />
        <!-- <wd-button type="primary" size="small" :loading="loading" @click="scanCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button> -->
      </view>
    </view>

    <!-- 产品列表 -->
    <view v-if="productList.length > 0" class="product-list bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">
        已录入产品 ({{ productList.length }})
      </view>

      <!-- 使用网格布局，一行两个 -->
      <view class="product-grid">
        <view class="product-item-card" v-for="(item, index) in productList" :key="index">
          <view class="product-item-content">
            <view class="product-number">{{ index + 1 }}.</view>
            <view class="product-code">{{ item }}</view>
            <wd-button
              type="error"
              size="small"
              plain
              @click="removeProduct(index)"
              class="delete-btn"
            >
              删除
            </wd-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 确认按钮 -->
    <view class="confirm-section p-4" style="margin-top: -10px">
      <wd-button
        type="primary"
        size="large"
        block
        :disabled="!canConfirm"
        :loading="loading"
        @click="confirmPacking"
      >
        确认
      </wd-button>
    </view>

    <!-- 确认产品信息弹框 -->
    <ConfirmProductDialog
      v-model="showConfirmDialog"
      :product-list="confirmProductList"
      :loading="loading"
      @confirm="submitPacking"
      @cancel="cancelConfirm"
    />

    <!-- 装箱打印弹框 -->
    <PackingPrintDialog
      v-model="showPrintDialog"
      :print-info="printInfo"
      :product-count="productList.length"
      :printing="printing"
      @print="printLabel"
      @close="closePrintDialog"
    />
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import type { WorkOrderOption, PrintInfo } from '@/types/packing'
import type { IResponsePackingInfo, IWorkOrderInfo } from '@/service/packing/index'

import ConfirmProductDialog from './components/ConfirmProductDialog.vue'
import PackingPrintDialog from './components/PackingPrintDialog.vue'
import { getWorkOrderListApi, submitPackingApi } from '../../service/packing/index'
import { parseCodeToObject } from '@/utils/parseCode'

defineOptions({
  name: 'PackingPage',
})

const toast = useToast()

// 响应式数据
const selectedWorkOrder = ref<string>('')
const workOrderInfo = ref<IWorkOrderInfo | null>(null)
const productCode = ref<string>('')
const productList = ref<string[]>([])
const showConfirmDialog = ref<boolean>(false)
const showPrintDialog = ref<boolean>(false)
const kdCode = ref<string>('')
const confirmProductList = ref<string[]>([])
const printInfo = ref<IResponsePackingInfo>({} as IResponsePackingInfo)
const loading = ref<boolean>(false)
const printing = ref<boolean>(false)
// 工单列表
const workOrderList = ref<IWorkOrderInfo[]>([])

// 工单选项
const workOrderOptions = ref<WorkOrderOption[]>([])

// 计算属性：是否可以确认
const canConfirm = computed(() => {
  return selectedWorkOrder.value && productList.value.length === workOrderInfo.value.singleQuantity
})

// 初始化数据
onMounted(async () => {
  await loadWorkOrderList()
})

// 加载工单列表
const loadWorkOrderList = async () => {
  try {
    const response = await getWorkOrderListApi()
    workOrderList.value = response.data
    if (response.code === 0) {
      workOrderOptions.value = response.data.map((item) => {
        return { label: item.workNo + '-' + item.productName, value: String(item.id) }
      })
      console.log(workOrderOptions.value)
    } else {
      toast.show(response.msg)
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
    toast.error('加载工单列表失败')
  }
}

// 工单变更处理
const onWorkOrderChange = async (value: any) => {
  console.log('选择的工单ID:', value)
  workOrderInfo.value = workOrderList.value.find((item) => item.id === Number(value.value))
  productList.value = []
}

// 扫码功能
const scanCode = async () => {
  try {
    loading.value = true
    toast.show('正在扫码...')
    const scannedCode = uni.scanCode({
      success: (res) => {
        console.log(res.scanType, '条码类型')
        productCode.value = res.result
      },
    })
    await addProductCode()
  } catch (error) {
    console.error('扫码失败:', error)
    toast.show('扫码失败')
  } finally {
    loading.value = false
  }
}

// 添加产品码
const addProductCode = async () => {
  if (!workOrderInfo.value) {
    toast.error('请先选择工单！')
    return
  }
  if (
    !parseCodeToObject(productCode.value.trim())[10] &&
    !parseCodeToObject(productCode.value.trim())[10].split('-')[1]
  ) {
    toast.error('零件号解析不存在！')
    return
  }
  if (
    workOrderInfo.value.partCode !== parseCodeToObject(productCode.value.trim())[10].split('-')[1]
  ) {
    toast.error({ msg: '零件号不匹配！', duration: 2000, iconName: 'error', iconSize: 50 })
    return
  }
  if (productList.value.length === workOrderInfo.value.singleQuantity) {
    toast.error(`一箱只能装${workOrderInfo.value.singleQuantity}个！`)
    return
  }
  if (!productCode.value.trim()) {
    toast.error('请输入产品码')
    return
  }

  if (productList.value.includes(productCode.value)) {
    toast.show('产品码已存在')
    return
  }

  productList.value.push(productCode.value)
  productCode.value = ''
  toast.show('产品码添加成功')
}

// 删除产品
const removeProduct = (index: number) => {
  productList.value.splice(index, 1)
  toast.show('产品已删除')
}

// 确认装箱
const confirmPacking = () => {
  if (!workOrderInfo.value) {
    toast.show('请先选择工单')
    return
  }

  // 生成确认产品列表
  confirmProductList.value = productList.value

  showConfirmDialog.value = true
}

// 取消确认
const cancelConfirm = () => {
  showConfirmDialog.value = false
}

// 提交装箱
const submitPacking = async () => {
  if (!workOrderInfo.value) {
    toast.show('工单信息不存在')
    return
  }

  try {
    loading.value = true
    showConfirmDialog.value = false

    const response = await submitPackingApi({
      workOrderId: workOrderInfo.value.id,
      productCodes: productList.value,
    })

    console.log(response)

    if (response.code === 0) {
      console.log(response, '###')

      printInfo.value = response.data

      // 设置打印信息
      // printInfo.value = {
      //   projectCode: workOrderInfo.value.projectCode,
      //   batchCode: workOrderInfo.value.batchCode,
      //   kdCode: response.data.kdCode,
      //   qrCodeData: response.data.qrCodeData,
      //   packingTime: response.data.packingTime,
      // }

      showPrintDialog.value = true
      toast.show('装箱成功')
    } else {
      toast.show(response.msg)
    }
  } catch (error) {
    console.error('装箱失败:', error)
    toast.show('装箱失败')
  } finally {
    loading.value = false
  }
}

// 关闭打印弹框
const closePrintDialog = () => {
  showPrintDialog.value = false
  // 重置数据
  productList.value = []
  selectedWorkOrder.value = ''
  workOrderInfo.value = null
  kdCode.value = ''
  confirmProductList.value = []
  printInfo.value = {} as IResponsePackingInfo
}

// 打印标签
const printLabel = () => {
  // 这里可以调用打印API或者调用设备打印功能
  printing.value = true
  toast.show('正在打印...')

  // 模拟打印过程
  setTimeout(() => {
    printing.value = false
    toast.show('打印完成')
    closePrintDialog()
  }, 2000)
}
</script>

<style scoped>
.packing-page {
  padding-bottom: 100px;
}

.section-title {
  padding-left: 8px;
  border-left: 4px solid #3b82f6;
}

.info-input {
  background-color: #f9fafb;
}

.product-item:last-child {
  border-bottom: none;
}
/* 网格布局 */
.product-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.product-item-card {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 2px 10px;
  background-color: #fafafa;
}

.product-item-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.product-number {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  flex-shrink: 0;
  width: 20px;
}

.product-code {
  font-size: 12px;
  color: #6b7280;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.delete-btn {
  flex-shrink: 0;
}

/* 响应式处理，小屏幕时回到单列 */
@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
}
</style>
